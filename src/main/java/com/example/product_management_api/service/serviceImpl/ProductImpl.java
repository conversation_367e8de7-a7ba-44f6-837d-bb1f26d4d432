package com.example.product_management_api.service.serviceImpl;

import com.example.product_management_api.dtos.request.ProductRequest;
import com.example.product_management_api.dtos.response.ProductResponse;
import com.example.product_management_api.service.ProductService;
import org.springframework.stereotype.Service;

@Service
public class ProductImpl implements ProductService {

    @Override
    public ProductResponse addProduct(ProductRequest productRequest) {
        if (productRequest == null){
            return
        }
    }
}
